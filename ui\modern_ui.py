# -*- coding: utf-8 -*-
"""
现代化团队管理UI
简洁大气的用户界面设计
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QFrame, QLabel, 
    QPushButton, QScrollArea, QStackedWidget, QSplitter, QTextEdit,
    QLineEdit, QSpinBox, QCheckBox, QProgressBar, QTableWidget,
    QTableWidgetItem, QHeaderView, QSizePolicy, QSpacerItem
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QLinearGradient
from ui.styles import StyleManager
from typing import Dict, List, Any


class ModernSidebar(QFrame):
    """现代化侧边栏导航"""
    
    page_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_page = "dashboard"
        self.buttons = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化侧边栏UI"""
        self.setFixedWidth(280)
        self.setStyleSheet(f"""
            QFrame {{
                background: {StyleManager.BACKGROUND_COLOR};
                border-right: 1px solid {StyleManager.NEUTRAL_MEDIUM};
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo区域
        logo_area = self.create_logo_area()
        layout.addWidget(logo_area)
        
        # 导航菜单
        nav_area = self.create_navigation_area()
        layout.addWidget(nav_area)
        
        # 底部区域
        layout.addStretch()
        bottom_area = self.create_bottom_area()
        layout.addWidget(bottom_area)
    
    def create_logo_area(self) -> QWidget:
        """创建Logo区域"""
        container = QFrame()
        container.setFixedHeight(80)
        container.setStyleSheet(f"""
            QFrame {{
                background: {StyleManager.PRIMARY_COLOR};
                border: none;
            }}
        """)
        
        layout = QHBoxLayout(container)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Logo图标
        logo_label = QLabel("🚀")
        logo_label.setStyleSheet("""
            font-size: 28px;
            color: white;
        """)
        
        # 应用标题
        title_label = QLabel("团队管理")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
        """)
        
        layout.addWidget(logo_label)
        layout.addWidget(title_label)
        layout.addStretch()
        
        return container
    
    def create_navigation_area(self) -> QWidget:
        """创建导航区域"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 20, 0, 0)
        layout.setSpacing(5)
        
        # 导航项目
        nav_items = [
            ("dashboard", "📊", "仪表盘"),
            ("invite", "✉️", "邀请成员"),
            ("manage", "👥", "团队管理"),
            ("batch", "⚡", "批量操作"),
            ("data", "📈", "数据分析"),
            ("settings", "⚙️", "设置")
        ]
        
        for page_id, icon, title in nav_items:
            btn = self.create_nav_button(page_id, icon, title)
            self.buttons[page_id] = btn
            layout.addWidget(btn)
        
        # 设置默认选中
        self.set_active_button("dashboard")
        
        return container
    
    def create_nav_button(self, page_id: str, icon: str, title: str) -> QPushButton:
        """创建导航按钮"""
        btn = QPushButton(f"{icon}  {title}")
        btn.setFixedHeight(50)
        btn.setStyleSheet(f"""
            QPushButton {{
                text-align: left;
                padding: 12px 20px;
                border: none;
                background: transparent;
                color: {StyleManager.get_color('TEXT')};
                font-size: 14px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
            }}
            QPushButton[active="true"] {{
                background: {StyleManager.get_color('PRIMARY')}15;
                color: {StyleManager.get_color('PRIMARY')};
                border-right: 3px solid {StyleManager.get_color('PRIMARY')};
                font-weight: bold;
            }}
        """)
        
        btn.clicked.connect(lambda: self.switch_page(page_id))
        return btn
    
    def create_bottom_area(self) -> QWidget:
        """创建底部区域"""
        container = QFrame()
        container.setFixedHeight(60)
        container.setStyleSheet(f"""
            QFrame {{
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
                border-top: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
            }}
        """)
        
        layout = QHBoxLayout(container)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # 状态指示器
        status_label = QLabel("● 已连接")
        status_label.setStyleSheet(f"""
            color: {StyleManager.get_color('SUCCESS')};
            font-size: 12px;
            font-weight: 500;
        """)
        
        layout.addWidget(status_label)
        layout.addStretch()
        
        return container
    
    def switch_page(self, page_id: str):
        """切换页面"""
        if page_id != self.current_page:
            self.set_active_button(page_id)
            self.current_page = page_id
            self.page_changed.emit(page_id)
    
    def set_active_button(self, page_id: str):
        """设置活动按钮"""
        for btn_id, btn in self.buttons.items():
            btn.setProperty("active", btn_id == page_id)
            btn.style().unpolish(btn)
            btn.style().polish(btn)


class ModernContentArea(QStackedWidget):
    """现代化内容区域"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.pages = {}
        self.init_pages()
    
    def init_pages(self):
        """初始化页面"""
        # 仪表盘页面
        dashboard_page = self.create_dashboard_page()
        self.addWidget(dashboard_page)
        self.pages["dashboard"] = dashboard_page
        
        # 邀请成员页面
        invite_page = self.create_invite_page()
        self.addWidget(invite_page)
        self.pages["invite"] = invite_page
        
        # 团队管理页面
        manage_page = self.create_manage_page()
        self.addWidget(manage_page)
        self.pages["manage"] = manage_page
        
        # 批量操作页面
        batch_page = self.create_batch_page()
        self.addWidget(batch_page)
        self.pages["batch"] = batch_page
        
        # 数据分析页面
        data_page = self.create_data_page()
        self.addWidget(data_page)
        self.pages["data"] = data_page
        
        # 设置页面
        settings_page = self.create_settings_page()
        self.addWidget(settings_page)
        self.pages["settings"] = settings_page
    
    def switch_to_page(self, page_id: str):
        """切换到指定页面"""
        if page_id in self.pages:
            page_widget = self.pages[page_id]
            self.setCurrentWidget(page_widget)
    
    def create_dashboard_page(self) -> QWidget:
        """创建仪表盘页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        
        # 页面标题
        title = QLabel("📊 仪表盘")
        title.setStyleSheet(f"""
            font-size: 28px;
            font-weight: bold;
            color: {StyleManager.get_color('TEXT')};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)
        
        # 统计卡片区域
        stats_area = self.create_stats_area()
        layout.addWidget(stats_area)
        
        # 快速操作区域
        quick_actions = self.create_quick_actions()
        layout.addWidget(quick_actions)
        
        # 最近活动
        recent_activity = self.create_recent_activity()
        layout.addWidget(recent_activity)
        
        layout.addStretch()
        return page
    
    def create_stats_area(self) -> QWidget:
        """创建统计区域"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setSpacing(20)
        
        # 统计卡片
        stats = [
            ("👥", "团队成员", "0", StyleManager.get_color('PRIMARY')),
            ("📧", "待处理邀请", "0", StyleManager.get_color('WARNING')),
            ("✅", "今日邀请", "0", StyleManager.get_color('SUCCESS')),
            ("📊", "成功率", "0%", StyleManager.get_color('INFO'))
        ]
        
        for icon, title, value, color in stats:
            card = StyleManager.create_stat_card(icon, title, value, color)
            layout.addWidget(card)
        
        return container
    
    def create_quick_actions(self) -> QWidget:
        """创建快速操作区域"""
        card = StyleManager.create_card("⚡ 快速操作")
        
        # 内容布局
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)
        
        # 快速操作按钮
        actions = [
            ("📧", "邀请成员", "primary"),
            ("🔄", "刷新数据", "info"),
            ("📊", "导出报告", "success"),
            ("⚙️", "系统设置", "secondary")
        ]
        
        for icon, text, style in actions:
            btn = StyleManager.create_button(f"{icon} {text}", style)
            btn.setFixedHeight(45)
            content_layout.addWidget(btn)
        
        # 将内容添加到卡片
        if card.layout():
            card.layout().addLayout(content_layout)
        
        return card
    
    def create_recent_activity(self) -> QWidget:
        """创建最近活动区域"""
        card = StyleManager.create_card("📋 最近活动")
        
        # 活动列表
        activity_list = QWidget()
        activity_layout = QVBoxLayout(activity_list)
        activity_layout.setSpacing(10)
        
        # 示例活动
        activities = [
            ("✅", "成功邀请用户 <EMAIL>", "2分钟前"),
            ("📧", "发送邀请给 5 个用户", "10分钟前"),
            ("🔄", "刷新团队数据", "1小时前"),
            ("⚙️", "更新系统配置", "2小时前")
        ]
        
        for icon, text, time in activities:
            activity_item = self.create_activity_item(icon, text, time)
            activity_layout.addWidget(activity_item)
        
        # 将活动列表添加到卡片
        if card.layout():
            card.layout().addWidget(activity_list)
        
        return card
    
    def create_activity_item(self, icon: str, text: str, time: str) -> QWidget:
        """创建活动项目"""
        item = QFrame()
        item.setStyleSheet(f"""
            QFrame {{
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
                border-radius: 6px;
                padding: 8px;
            }}
        """)
        
        layout = QHBoxLayout(item)
        layout.setContentsMargins(12, 8, 12, 8)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 16px;")
        
        # 文本
        text_label = QLabel(text)
        text_label.setStyleSheet(f"color: {StyleManager.get_color('TEXT')}; font-weight: 500;")
        
        # 时间
        time_label = QLabel(time)
        time_label.setStyleSheet(f"color: {StyleManager.get_color('NEUTRAL_DARK')}; font-size: 12px;")
        
        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        layout.addStretch()
        layout.addWidget(time_label)
        
        return item

    def create_invite_page(self) -> QWidget:
        """创建邀请成员页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # 页面标题
        title = QLabel("✉️ 邀请成员")
        title.setStyleSheet(f"""
            font-size: 28px;
            font-weight: bold;
            color: {StyleManager.get_color('TEXT')};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # 邀请表单卡片
        invite_card = self.create_invite_form_card()
        layout.addWidget(invite_card)

        # 邀请历史卡片
        history_card = self.create_invite_history_card()
        layout.addWidget(history_card)

        layout.addStretch()
        return page

    def create_invite_form_card(self) -> QWidget:
        """创建邀请表单卡片"""
        card = StyleManager.create_card("📧 发送邀请")

        # 表单布局
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)

        # 邮箱输入区域
        email_section = QWidget()
        email_layout = QVBoxLayout(email_section)
        email_layout.setSpacing(8)

        email_label = QLabel("邮箱地址（每行一个）:")
        email_label.setStyleSheet(f"color: {StyleManager.get_color('TEXT')}; font-weight: 500;")

        self.email_input = QTextEdit()
        self.email_input.setFixedHeight(120)
        self.email_input.setPlaceholderText("请输入邮箱地址，每行一个\n例如：\<EMAIL>\<EMAIL>")
        self.email_input.setStyleSheet(f"""
            QTextEdit {{
                border: 2px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                background: {StyleManager.get_color('BACKGROUND')};
            }}
            QTextEdit:focus {{
                border-color: {StyleManager.get_color('PRIMARY')};
            }}
        """)

        email_layout.addWidget(email_label)
        email_layout.addWidget(self.email_input)

        # 操作按钮区域
        button_section = QWidget()
        button_layout = QHBoxLayout(button_section)
        button_layout.setSpacing(15)

        # 发送邀请按钮
        send_btn = StyleManager.create_button("📧 发送邀请", "primary")
        send_btn.setFixedHeight(45)

        # 清空按钮
        clear_btn = StyleManager.create_button("🗑️ 清空", "secondary")
        clear_btn.setFixedHeight(45)

        # 导入按钮
        import_btn = StyleManager.create_button("📁 导入文件", "info")
        import_btn.setFixedHeight(45)

        button_layout.addWidget(send_btn)
        button_layout.addWidget(clear_btn)
        button_layout.addWidget(import_btn)
        button_layout.addStretch()

        form_layout.addWidget(email_section)
        form_layout.addWidget(button_section)

        # 将表单添加到卡片
        if card.layout():
            card.layout().addLayout(form_layout)

        return card

    def create_invite_history_card(self) -> QWidget:
        """创建邀请历史卡片"""
        card = StyleManager.create_card("📋 邀请历史")

        # 历史列表
        history_widget = QWidget()
        history_layout = QVBoxLayout(history_widget)

        # 表格
        self.invite_table = QTableWidget()
        self.invite_table.setColumnCount(4)
        self.invite_table.setHorizontalHeaderLabels(["邮箱", "状态", "邀请时间", "操作"])

        # 设置表格样式
        self.invite_table.setStyleSheet(f"""
            QTableWidget {{
                border: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 8px;
                background: {StyleManager.get_color('BACKGROUND')};
                gridline-color: {StyleManager.get_color('NEUTRAL_LIGHT')};
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {StyleManager.get_color('NEUTRAL_LIGHT')};
            }}
            QHeaderView::section {{
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
                padding: 10px;
                border: none;
                font-weight: bold;
                color: {StyleManager.get_color('TEXT')};
            }}
        """)

        # 设置表格属性
        header = self.invite_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)

        self.invite_table.setAlternatingRowColors(True)
        self.invite_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        history_layout.addWidget(self.invite_table)

        # 将历史列表添加到卡片
        if card.layout():
            card.layout().addWidget(history_widget)

        return card

    def create_manage_page(self) -> QWidget:
        """创建团队管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # 页面标题
        title = QLabel("👥 团队管理")
        title.setStyleSheet(f"""
            font-size: 28px;
            font-weight: bold;
            color: {StyleManager.get_color('TEXT')};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # 团队信息卡片
        team_info_card = self.create_team_info_card()
        layout.addWidget(team_info_card)

        # 成员列表卡片
        members_card = self.create_members_list_card()
        layout.addWidget(members_card)

        layout.addStretch()
        return page

    def create_team_info_card(self) -> QWidget:
        """创建团队信息卡片"""
        card = StyleManager.create_card("ℹ️ 团队信息")

        # 信息布局
        info_layout = QHBoxLayout()
        info_layout.setSpacing(30)

        # 团队统计
        stats_section = QWidget()
        stats_layout = QVBoxLayout(stats_section)

        # 统计项目
        stats_items = [
            ("总成员数", "0"),
            ("活跃成员", "0"),
            ("待处理邀请", "0"),
            ("今日新增", "0")
        ]

        for label, value in stats_items:
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(0, 5, 0, 5)

            label_widget = QLabel(label + ":")
            label_widget.setStyleSheet(f"color: {StyleManager.get_color('NEUTRAL_DARK')}; font-weight: 500;")

            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"color: {StyleManager.get_color('PRIMARY')}; font-weight: bold; font-size: 16px;")

            item_layout.addWidget(label_widget)
            item_layout.addStretch()
            item_layout.addWidget(value_widget)

            stats_layout.addWidget(item_widget)

        # 操作按钮区域
        actions_section = QWidget()
        actions_layout = QVBoxLayout(actions_section)
        actions_layout.setSpacing(10)

        refresh_btn = StyleManager.create_button("🔄 刷新数据", "primary")
        export_btn = StyleManager.create_button("📊 导出数据", "success")
        settings_btn = StyleManager.create_button("⚙️ 团队设置", "info")

        actions_layout.addWidget(refresh_btn)
        actions_layout.addWidget(export_btn)
        actions_layout.addWidget(settings_btn)
        actions_layout.addStretch()

        info_layout.addWidget(stats_section, 2)
        info_layout.addWidget(actions_section, 1)

        # 将信息布局添加到卡片
        if card.layout():
            card.layout().addLayout(info_layout)

        return card

    def create_members_list_card(self) -> QWidget:
        """创建成员列表卡片"""
        card = StyleManager.create_card("👥 成员列表")

        # 成员表格
        self.members_table = QTableWidget()
        self.members_table.setColumnCount(5)
        self.members_table.setHorizontalHeaderLabels(["姓名", "邮箱", "状态", "加入时间", "操作"])

        # 设置表格样式
        self.members_table.setStyleSheet(f"""
            QTableWidget {{
                border: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 8px;
                background: {StyleManager.get_color('BACKGROUND')};
                gridline-color: {StyleManager.get_color('NEUTRAL_LIGHT')};
            }}
            QTableWidget::item {{
                padding: 12px 8px;
                border-bottom: 1px solid {StyleManager.get_color('NEUTRAL_LIGHT')};
            }}
            QHeaderView::section {{
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                color: {StyleManager.get_color('TEXT')};
            }}
        """)

        # 设置表格属性
        header = self.members_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)

        self.members_table.setAlternatingRowColors(True)
        self.members_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # 将表格添加到卡片
        if card.layout():
            card.layout().addWidget(self.members_table)

        return card

    def create_batch_page(self) -> QWidget:
        """创建批量操作页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # 页面标题
        title = QLabel("⚡ 批量操作")
        title.setStyleSheet(f"""
            font-size: 28px;
            font-weight: bold;
            color: {StyleManager.get_color('TEXT')};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # 批量邀请卡片
        batch_invite_card = self.create_batch_invite_card()
        layout.addWidget(batch_invite_card)

        # 批量管理卡片
        batch_manage_card = self.create_batch_manage_card()
        layout.addWidget(batch_manage_card)

        layout.addStretch()
        return page

    def create_batch_invite_card(self) -> QWidget:
        """创建批量邀请卡片"""
        card = StyleManager.create_card("📧 批量邀请")

        # 内容布局
        content_layout = QVBoxLayout()
        content_layout.setSpacing(20)

        # 文件导入区域
        import_section = QWidget()
        import_layout = QHBoxLayout(import_section)
        import_layout.setSpacing(15)

        import_label = QLabel("导入邮箱文件:")
        import_label.setStyleSheet(f"color: {StyleManager.get_color('TEXT')}; font-weight: 500;")

        import_btn = StyleManager.create_button("📁 选择文件", "info")
        import_btn.setFixedHeight(40)

        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet(f"color: {StyleManager.get_color('NEUTRAL_DARK')}; font-style: italic;")

        import_layout.addWidget(import_label)
        import_layout.addWidget(import_btn)
        import_layout.addWidget(self.file_path_label)
        import_layout.addStretch()

        # 批量操作按钮区域
        batch_actions = QWidget()
        batch_layout = QHBoxLayout(batch_actions)
        batch_layout.setSpacing(15)

        batch_invite_btn = StyleManager.create_button("📧 批量邀请", "primary")
        batch_invite_btn.setFixedHeight(45)

        validate_btn = StyleManager.create_button("✅ 验证邮箱", "success")
        validate_btn.setFixedHeight(45)

        preview_btn = StyleManager.create_button("👁️ 预览", "info")
        preview_btn.setFixedHeight(45)

        batch_layout.addWidget(batch_invite_btn)
        batch_layout.addWidget(validate_btn)
        batch_layout.addWidget(preview_btn)
        batch_layout.addStretch()

        content_layout.addWidget(import_section)
        content_layout.addWidget(batch_actions)

        # 将内容添加到卡片
        if card.layout():
            card.layout().addLayout(content_layout)

        return card

    def create_batch_manage_card(self) -> QWidget:
        """创建批量管理卡片"""
        card = StyleManager.create_card("⚙️ 批量管理")

        # 内容布局
        content_layout = QVBoxLayout()
        content_layout.setSpacing(20)

        # 计划切换区域
        plan_section = QWidget()
        plan_layout = QHBoxLayout(plan_section)
        plan_layout.setSpacing(15)

        plan_label = QLabel("计划管理:")
        plan_label.setStyleSheet(f"color: {StyleManager.get_color('TEXT')}; font-weight: 500;")

        community_btn = StyleManager.create_button("👥 社区计划", "secondary")
        community_btn.setFixedHeight(40)

        max_btn = StyleManager.create_button("⭐ 最大计划", "warning")
        max_btn.setFixedHeight(40)

        plan_layout.addWidget(plan_label)
        plan_layout.addWidget(community_btn)
        plan_layout.addWidget(max_btn)
        plan_layout.addStretch()

        # 批量删除区域
        delete_section = QWidget()
        delete_layout = QHBoxLayout(delete_section)
        delete_layout.setSpacing(15)

        delete_label = QLabel("批量删除:")
        delete_label.setStyleSheet(f"color: {StyleManager.get_color('TEXT')}; font-weight: 500;")

        delete_pending_btn = StyleManager.create_button("🗑️ 删除待处理", "danger")
        delete_pending_btn.setFixedHeight(40)

        delete_all_btn = StyleManager.create_button("⚠️ 删除全部", "danger")
        delete_all_btn.setFixedHeight(40)

        delete_layout.addWidget(delete_label)
        delete_layout.addWidget(delete_pending_btn)
        delete_layout.addWidget(delete_all_btn)
        delete_layout.addStretch()

        content_layout.addWidget(plan_section)
        content_layout.addWidget(delete_section)

        # 将内容添加到卡片
        if card.layout():
            card.layout().addLayout(content_layout)

        return card

    def create_data_page(self) -> QWidget:
        """创建数据分析页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # 页面标题
        title = QLabel("📈 数据分析")
        title.setStyleSheet(f"""
            font-size: 28px;
            font-weight: bold;
            color: {StyleManager.get_color('TEXT')};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # 数据概览卡片
        overview_card = self.create_data_overview_card()
        layout.addWidget(overview_card)

        # 详细数据卡片
        details_card = self.create_data_details_card()
        layout.addWidget(details_card)

        layout.addStretch()
        return page

    def create_data_overview_card(self) -> QWidget:
        """创建数据概览卡片"""
        card = StyleManager.create_card("📊 数据概览")

        # 概览统计
        overview_layout = QHBoxLayout()
        overview_layout.setSpacing(20)

        # 统计卡片
        stats = [
            ("📧", "总邀请数", "0", StyleManager.get_color('PRIMARY')),
            ("✅", "成功邀请", "0", StyleManager.get_color('SUCCESS')),
            ("⏳", "待处理", "0", StyleManager.get_color('WARNING')),
            ("❌", "失败邀请", "0", StyleManager.get_color('DANGER'))
        ]

        for icon, title, value, color in stats:
            stat_card = StyleManager.create_stat_card(icon, title, value, color)
            overview_layout.addWidget(stat_card)

        # 将概览添加到卡片
        if card.layout():
            card.layout().addLayout(overview_layout)

        return card

    def create_data_details_card(self) -> QWidget:
        """创建详细数据卡片"""
        card = StyleManager.create_card("📋 详细数据")

        # 数据表格
        self.data_table = QTableWidget()
        self.data_table.setColumnCount(6)
        self.data_table.setHorizontalHeaderLabels(["邮箱", "状态", "邀请时间", "响应时间", "计划", "操作"])

        # 设置表格样式
        self.data_table.setStyleSheet(f"""
            QTableWidget {{
                border: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 8px;
                background: {StyleManager.get_color('BACKGROUND')};
                gridline-color: {StyleManager.get_color('NEUTRAL_LIGHT')};
            }}
            QTableWidget::item {{
                padding: 10px 8px;
                border-bottom: 1px solid {StyleManager.get_color('NEUTRAL_LIGHT')};
            }}
            QHeaderView::section {{
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                color: {StyleManager.get_color('TEXT')};
            }}
        """)

        # 设置表格属性
        header = self.data_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)

        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # 将表格添加到卡片
        if card.layout():
            card.layout().addWidget(self.data_table)

        return card

    def create_settings_page(self) -> QWidget:
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # 页面标题
        title = QLabel("⚙️ 系统设置")
        title.setStyleSheet(f"""
            font-size: 28px;
            font-weight: bold;
            color: {StyleManager.get_color('TEXT')};
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # 设置卡片区域
        settings_layout = QHBoxLayout()
        settings_layout.setSpacing(25)

        # API设置卡片
        api_card = self.create_api_settings_card()
        settings_layout.addWidget(api_card)

        # 界面设置卡片
        ui_card = self.create_ui_settings_card()
        settings_layout.addWidget(ui_card)

        layout.addLayout(settings_layout)
        layout.addStretch()
        return page

    def create_api_settings_card(self) -> QWidget:
        """创建API设置卡片"""
        card = StyleManager.create_card("🔗 API设置")

        # 设置表单
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)

        # Cookie设置
        cookie_section = QWidget()
        cookie_layout = QVBoxLayout(cookie_section)
        cookie_layout.setSpacing(8)

        cookie_label = QLabel("Cookie:")
        cookie_label.setStyleSheet(f"color: {StyleManager.get_color('TEXT')}; font-weight: 500;")

        self.cookie_input = QTextEdit()
        self.cookie_input.setFixedHeight(80)
        self.cookie_input.setPlaceholderText("请输入Cookie信息...")
        self.cookie_input.setStyleSheet(f"""
            QTextEdit {{
                border: 2px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                background: {StyleManager.get_color('BACKGROUND')};
            }}
            QTextEdit:focus {{
                border-color: {StyleManager.get_color('PRIMARY')};
            }}
        """)

        cookie_layout.addWidget(cookie_label)
        cookie_layout.addWidget(self.cookie_input)

        # 按钮区域
        button_section = QWidget()
        button_layout = QHBoxLayout(button_section)
        button_layout.setSpacing(10)

        test_btn = StyleManager.create_button("🔍 测试连接", "info")
        test_btn.setFixedHeight(40)

        save_btn = StyleManager.create_button("💾 保存设置", "primary")
        save_btn.setFixedHeight(40)

        button_layout.addWidget(test_btn)
        button_layout.addWidget(save_btn)
        button_layout.addStretch()

        form_layout.addWidget(cookie_section)
        form_layout.addWidget(button_section)

        # 将表单添加到卡片
        if card.layout():
            card.layout().addLayout(form_layout)

        return card

    def create_ui_settings_card(self) -> QWidget:
        """创建界面设置卡片"""
        card = StyleManager.create_card("🎨 界面设置")

        # 设置表单
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)

        # 主题设置
        theme_section = QWidget()
        theme_layout = QHBoxLayout(theme_section)
        theme_layout.setSpacing(10)

        theme_label = QLabel("主题:")
        theme_label.setStyleSheet(f"color: {StyleManager.get_color('TEXT')}; font-weight: 500;")

        light_btn = StyleManager.create_button("☀️ 浅色", "secondary")
        light_btn.setFixedHeight(35)

        dark_btn = StyleManager.create_button("🌙 深色", "secondary")
        dark_btn.setFixedHeight(35)

        theme_layout.addWidget(theme_label)
        theme_layout.addWidget(light_btn)
        theme_layout.addWidget(dark_btn)
        theme_layout.addStretch()

        # 自动刷新设置
        refresh_section = QWidget()
        refresh_layout = QHBoxLayout(refresh_section)
        refresh_layout.setSpacing(10)

        refresh_label = QLabel("自动刷新:")
        refresh_label.setStyleSheet(f"color: {StyleManager.get_color('TEXT')}; font-weight: 500;")

        self.auto_refresh_check = QCheckBox("启用自动刷新")
        self.auto_refresh_check.setStyleSheet(f"color: {StyleManager.get_color('TEXT')};")

        self.refresh_interval = QSpinBox()
        self.refresh_interval.setRange(10, 300)
        self.refresh_interval.setValue(30)
        self.refresh_interval.setSuffix(" 秒")
        self.refresh_interval.setStyleSheet(f"""
            QSpinBox {{
                border: 2px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 6px;
                padding: 5px;
                background: {StyleManager.get_color('BACKGROUND')};
            }}
        """)

        refresh_layout.addWidget(refresh_label)
        refresh_layout.addWidget(self.auto_refresh_check)
        refresh_layout.addWidget(self.refresh_interval)
        refresh_layout.addStretch()

        form_layout.addWidget(theme_section)
        form_layout.addWidget(refresh_section)

        # 将表单添加到卡片
        if card.layout():
            card.layout().addLayout(form_layout)

        return card


class ModernTeamManagerUI(QMainWindow):
    """现代化团队管理主界面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("团队管理工具 - 现代版")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 初始化UI
        self.init_ui()
        self.apply_modern_styles()

    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建侧边栏
        self.sidebar = ModernSidebar()
        self.sidebar.page_changed.connect(self.switch_page)

        # 创建内容区域
        self.content_area = ModernContentArea()

        # 添加到主布局
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(self.content_area, 1)

        # 设置默认页面
        self.content_area.switch_to_page("dashboard")

    def switch_page(self, page_id: str):
        """切换页面"""
        self.content_area.switch_to_page(page_id)

    def apply_modern_styles(self):
        """应用现代化样式"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
            }}
        """)

        # 添加窗口阴影效果
        StyleManager.apply_shadow_effect(self, blur_radius=10, offset=(0, 5))
