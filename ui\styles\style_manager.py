# -*- coding: utf-8 -*-
"""
现代化样式管理器
提供组件创建和样式应用功能
"""

from PyQt6.QtWidgets import (
    QPushButton, QFrame, QLabel, QVBoxLayout, QHBoxLayout, QWidget,
    QGraphicsDropShadowEffect
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor
from config.constants import COLOR_THEMES


class StyleManager:
    """现代化样式管理器"""
    
    # 当前主题颜色（默认浅色主题）
    _current_colors = COLOR_THEMES['light']
    
    @classmethod
    def set_theme_colors(cls, theme_name: str):
        """
        设置当前主题颜色
        
        Args:
            theme_name: 主题名称
        """
        if theme_name in COLOR_THEMES:
            cls._current_colors = COLOR_THEMES[theme_name]
        else:
            print(f"⚠️ 未知主题: {theme_name}，使用默认主题")
            cls._current_colors = COLOR_THEMES['light']
    
    @classmethod
    def get_color(cls, color_name: str) -> str:
        """
        获取当前主题的颜色值
        
        Args:
            color_name: 颜色名称
            
        Returns:
            颜色值
        """
        return cls._current_colors.get(color_name, '#000000')
    
    # 颜色常量（兼容性）
    PRIMARY_COLOR = '#2563eb'
    PRIMARY_LIGHT = '#60a5fa'
    PRIMARY_DARK = '#1d4ed8'
    SECONDARY_COLOR = '#6366f1'
    SUCCESS_COLOR = '#10b981'
    DANGER_COLOR = '#ef4444'
    WARNING_COLOR = '#f59e0b'
    INFO_COLOR = '#06b6d4'
    BACKGROUND_COLOR = '#ffffff'
    TEXT_COLOR = '#111827'
    NEUTRAL_LIGHT = '#f9fafb'
    NEUTRAL_MEDIUM = '#e5e7eb'
    NEUTRAL_DARK = '#374151'
    
    @staticmethod
    def apply_shadow_effect(widget, blur_radius=6, offset=(0, 2), color=None):
        """
        为组件添加阴影效果
        
        Args:
            widget: 目标组件
            blur_radius: 模糊半径
            offset: 阴影偏移 (x, y)
            color: 阴影颜色
        """
        if color is None:
            color = QColor(0, 0, 0, 20)
        
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setXOffset(offset[0])
        shadow.setYOffset(offset[1])
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)
    
    @classmethod
    def create_button(cls, text: str, button_class: str = "", icon: str = None) -> QPushButton:
        """
        创建样式化按钮
        
        Args:
            text: 按钮文本
            button_class: 按钮样式类别
            icon: 图标文本
            
        Returns:
            样式化的按钮
        """
        button = QPushButton(text)
        
        if button_class:
            button.setProperty("class", button_class)
        
        if icon:
            button.setText(f"{icon} {text}")
        
        # 添加阴影效果
        cls.apply_shadow_effect(button, blur_radius=4)
        
        return button
    
    @classmethod
    def create_card(cls, title: str = "", content_widget: QWidget = None, icon: str = None) -> QFrame:
        """
        创建卡片容器
        
        Args:
            title: 卡片标题
            content_widget: 内容组件
            icon: 标题图标
            
        Returns:
            卡片容器
        """
        card = QFrame()
        card.setFrameShape(QFrame.Shape.StyledPanel)
        card.setFrameShadow(QFrame.Shadow.Raised)
        
        # 设置卡片样式
        card.setStyleSheet(f"""
            QFrame {{
                background: {cls.get_color('BACKGROUND')};
                border: 1px solid {cls.get_color('NEUTRAL_MEDIUM')};
                border-radius: 8px;
                padding: 16px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)
        
        # 添加标题
        if title:
            header = QHBoxLayout()
            
            # 添加图标
            if icon:
                icon_label = QLabel(icon)
                icon_label.setStyleSheet(f"""
                    font-size: 16px;
                    color: {cls.get_color('PRIMARY')};
                    margin-right: 8px;
                """)
                header.addWidget(icon_label)
            
            # 添加标题文本
            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                font-size: 16px;
                font-weight: bold;
                color: {cls.get_color('PRIMARY')};
            """)
            header.addWidget(title_label)
            header.addStretch()
            layout.addLayout(header)
            
            # 添加分隔线
            separator = QFrame()
            separator.setFrameShape(QFrame.Shape.HLine)
            separator.setFrameShadow(QFrame.Shadow.Sunken)
            separator.setStyleSheet(f"background-color: {cls.get_color('NEUTRAL_MEDIUM')};")
            layout.addWidget(separator)
        
        # 添加内容组件
        if content_widget:
            layout.addWidget(content_widget)
        
        # 添加阴影效果
        cls.apply_shadow_effect(card)
        
        return card
    
    @classmethod
    def create_stat_card(cls, icon: str, title: str, value: str, color: str = None) -> QFrame:
        """
        创建统计卡片
        
        Args:
            icon: 图标
            title: 标题
            value: 数值
            color: 主题色
            
        Returns:
            统计卡片
        """
        if color is None:
            color = cls.get_color('PRIMARY')
        
        card = QFrame()
        card.setFixedHeight(60)
        card.setStyleSheet(f"""
            QFrame {{
                background: {cls.get_color('BACKGROUND')};
                border-radius: 8px;
                border-left: 4px solid {color};
            }}
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(f"""
            font-size: 20px;
            color: {color};
            background: {color}10;
            border-radius: 15px;
            min-width: 30px;
            min-height: 30px;
            padding: 2px;
        """)
        
        # 内容布局
        content_layout = QHBoxLayout()
        content_layout.setSpacing(5)
        content_layout.setContentsMargins(5, 0, 0, 0)
        
        # 数值标签
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: 20px;
            font-weight: bold;
            color: {color};
            margin-right: 5px;
        """)
        
        # 标题标签
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 12px;
            color: {cls.get_color('NEUTRAL_DARK')};
        """)
        
        content_layout.addWidget(value_label)
        content_layout.addWidget(title_label)
        content_layout.addStretch()
        
        # 组装布局
        layout.addWidget(icon_label)
        layout.addLayout(content_layout)
        
        # 添加阴影效果
        cls.apply_shadow_effect(card, blur_radius=5, offset=(0, 2))
        
        # 存储值标签以便后续更新
        card.value_label = value_label
        
        return card
    
    @classmethod
    def create_modern_header(cls, title: str, subtitle: str = "", icon: str = "🚀") -> QWidget:
        """
        创建现代化头部组件
        
        Args:
            title: 主标题
            subtitle: 副标题
            icon: 图标
            
        Returns:
            头部组件
        """
        header = QFrame()
        header.setFixedHeight(100)
        header.setObjectName("header")
        header.setStyleSheet(f"""
            #header {{
                background: {cls.get_color('PRIMARY')};
                border-radius: 8px;
            }}
        """)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(20, 12, 20, 12)
        
        # 左侧容器
        left_container = QWidget()
        left_layout = QHBoxLayout(left_container)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(15)
        
        # 图标
        logo_label = QLabel(icon)
        logo_label.setStyleSheet("""
            font-size: 32px;
            color: white;
        """)
        
        # 标题容器
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(5)
        
        # 主标题
        main_title = QLabel(title)
        main_title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: white;
        """)
        
        title_layout.addWidget(main_title)
        
        # 副标题
        if subtitle:
            sub_title = QLabel(subtitle)
            sub_title.setStyleSheet("""
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
            """)
            title_layout.addWidget(sub_title)
        
        left_layout.addWidget(logo_label)
        left_layout.addWidget(title_container)
        
        layout.addWidget(left_container, 1)
        layout.addStretch()
        
        # 添加阴影效果
        cls.apply_shadow_effect(header)
        
        return header
    
    @classmethod
    def get_status_style(cls, status: str) -> str:
        """
        获取状态样式
        
        Args:
            status: 状态类型 (success, error, warning, info)
            
        Returns:
            CSS样式字符串
        """
        styles = {
            'success': f"""
                background: {cls.get_color('SUCCESS')}20;
                border: 1px solid {cls.get_color('SUCCESS')};
                color: {cls.get_color('SUCCESS')};
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
            """,
            'error': f"""
                background: {cls.get_color('DANGER')}20;
                border: 1px solid {cls.get_color('DANGER')};
                color: {cls.get_color('DANGER')};
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
            """,
            'warning': f"""
                background: {cls.get_color('WARNING')}20;
                border: 1px solid {cls.get_color('WARNING')};
                color: {cls.get_color('WARNING')};
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
            """,
            'info': f"""
                background: {cls.get_color('INFO')}20;
                border: 1px solid {cls.get_color('INFO')};
                color: {cls.get_color('INFO')};
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
            """
        }
        
        return styles.get(status, styles['info'])
